package com.aispeech.hybridspeech.asr.offline

import android.content.Context
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.TranscriptionResult
import com.aispeech.hybridspeech.UnifiedJson
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

/**
 * 离线转写协调器
 * 协调PCM数据流和离线转写引擎
 */
class OfflineOrchestrator(private val context: Context) : CoroutineScope {

  companion object {
    private const val TAG = "OfflineOrchestrator"
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private val offlineEngine = OfflineEngine()

  // 初始化状态跟踪
  private var isEngineInitialized = false
  private var initializationInProgress = false

  private val _transcriptionResultFlow = MutableSharedFlow<TranscriptionResult>(
    replay = 0,
    extraBufferCapacity = 100
  )
  val transcriptionResultFlow: SharedFlow<TranscriptionResult> = _transcriptionResultFlow.asSharedFlow()

  private var isRunning = false
  private var pcmProcessingJob: Job? = null
  private var resultProcessingJob: Job? = null

  // 添加用户暂停状态管理
  @Volatile
  private var isPausedByUser = false // 用户主动暂停的标志

  init {
    // 设置引擎状态变化监听器
    offlineEngine.setStatusChangeListener { engineStatus ->
      isEngineInitialized = engineStatus.isInitialized && !engineStatus.isInitializing
      initializationInProgress = engineStatus.isInitializing

      // 记录状态变化
      AILog.d(TAG, "Engine status updated - initialized: $isEngineInitialized, initializing: $initializationInProgress, state: ${engineStatus.engineState::class.simpleName}")
    }
  }

  /**
   * 设置离线引擎配置并异步初始化
   */
  fun setConfig(modelPath: String): Boolean {
    try {
      if (initializationInProgress) {
        AILog.w(TAG, "Initialization already in progress")
        return false
      }

      if (isEngineInitialized) {
        AILog.w(TAG, "Engine already initialized")
        return true
      }

      offlineEngine.setModelPath(modelPath)
      val initStarted = offlineEngine.initializeAsync()
      if (initStarted) {
        initializationInProgress = true
        AILog.i(TAG, "Engine async initialization started...")
      }
      return initStarted
    } catch (e: Exception) {
      AILog.e(TAG, "Error setting offline config", e)
      initializationInProgress = false
      return false
    }
  }

  /**
   * 开始离线转写处理
   * 支持在初始化期间启动，PCM数据会被缓存直到初始化完成
   */
  fun start(pcmDataFlow: SharedFlow<ByteArray>): Boolean {
    try {
      if (isRunning) {
        AILog.w(TAG, "Already running")
        return true
      }

      // 检查是否已配置
      if (!initializationInProgress && !isEngineInitialized) {
        AILog.e(TAG, "Cannot start: engine not configured. Call setConfig() first")
        return false
      }

      // 启动引擎（无论是否已初始化，引擎会自己处理）
      if (!offlineEngine.start()) {
        AILog.e(TAG, "Failed to start offline engine")
        return false
      }

      // 启动数据处理流程（即使引擎还在初始化，数据也会被缓存）
      startDataProcessing(pcmDataFlow)

      isRunning = true
      AILog.i(TAG, "Offline orchestrator started (engine initialization status: ${if (isEngineInitialized) "completed" else "in progress"})")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Error starting offline orchestrator", e)
      return false
    }
  }

  /**
   * 停止离线转写处理
   */
  fun stop() {
    isRunning = false

    // 停止数据处理
    pcmProcessingJob?.cancel()
    resultProcessingJob?.cancel()

    // 停止离线引擎
    offlineEngine.stop()

    AILog.i(TAG, "Offline orchestrator stopped")
  }

  /**
   * 释放资源
   */
  fun release() {
    stop()
    offlineEngine.release()
    isEngineInitialized = false
    initializationInProgress = false
    shutdown("Orchestrator released")
    AILog.i(TAG, "Offline orchestrator released")
  }

  /**
   * 关闭协调器
   * 包括释放引擎资源和关闭协程作用域
   */
  fun shutdown(reason: String = "OfflineOrchestrator shutdown") {
    // 先释放引擎资源
    if (isEngineInitialized) {
      release()
    } else {
      stop() // 如果引擎未初始化，至少要停止处理
    }

    // 关闭协程作用域
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "Offline orchestrator shut down: $reason")
  }

  /**
   * 启动数据处理流程
   */
  private fun startDataProcessing(pcmDataFlow: SharedFlow<ByteArray>) {
    // 处理PCM数据流
    pcmProcessingJob = launch {
      pcmDataFlow.collect { pcmData ->
        try {
          // 将PCM数据送入离线引擎
          offlineEngine.processPcmData(pcmData)
        } catch (e: Exception) {
          AILog.e(TAG, "Error processing PCM data", e)
        }
      }
    }

    // 处理转写结果
    resultProcessingJob = launch {
      offlineEngine.transcriptionResultFlow.collect { offlineResult ->
        try {
          val transcriptionResult = UnifiedJson.decodeFromString<TranscriptionResult>(offlineResult)
          _transcriptionResultFlow.tryEmit(transcriptionResult)
        } catch (e: Exception) {
          AILog.e(TAG, "Error processing transcription result", e)
        }
      }
    }
  }

  /**
   * 检查是否可以启动
   */
  fun canStart(): Boolean {
    return isEngineInitialized && !initializationInProgress && !isRunning
  }

  /**
   * 暂停离线转写处理
   * 暂停数据处理协程，但保留引擎状态用于恢复
   */
  suspend fun pauseOfflineAsr() {
    if (!isRunning) {
      AILog.w(TAG, "Cannot pause: not running")
      return
    }

    if (isPausedByUser) {
      AILog.w(TAG, "Already paused by user")
      return
    }

    AILog.i(TAG, "Pausing offline ASR processing")

    try {
      isPausedByUser = true

      // 暂停数据处理协程
      pcmProcessingJob?.cancel()
      pcmProcessingJob = null

      resultProcessingJob?.cancel()
      resultProcessingJob = null

      // 暂停离线引擎处理，清空缓存释放内存
      offlineEngine.pause()

      AILog.i(TAG, "Offline ASR paused successfully")
    } catch (e: Exception) {
      AILog.e(TAG, "Error during pause operation", e)
      // 如果暂停过程中出现错误，确保状态一致性
      isPausedByUser = true
      throw e
    }
  }

  /**
   * 恢复离线转写处理
   * 重新启动数据处理协程
   */
  suspend fun resumeOfflineAsr(pcmDataFlow: SharedFlow<ByteArray>) {
    if (!isRunning) {
      AILog.w(TAG, "Cannot resume: not running")
      return
    }

    if (!isPausedByUser) {
      AILog.w(TAG, "Not paused by user, no need to resume")
      return
    }

    AILog.i(TAG, "Resuming offline ASR processing")

    try {
      // 检查引擎是否仍然可用
      if (!isEngineInitialized) {
        AILog.e(TAG, "Engine not initialized, cannot resume")
        throw IllegalStateException("Engine not initialized")
      }

      // 恢复离线引擎处理
      offlineEngine.resume()

      // 重新启动数据处理流程
      startDataProcessing(pcmDataFlow)

      // 清除暂停标志
      isPausedByUser = false

      AILog.i(TAG, "Offline ASR resumed successfully")
    } catch (e: Exception) {
      AILog.e(TAG, "Error during resume operation", e)
      // 如果恢复失败，保持暂停状态
      throw e
    }
  }

  /**
   * 检查是否被用户暂停
   */
  fun isPausedByUser(): Boolean {
    return isPausedByUser
  }


  /**
   * 获取当前状态
   */
  fun getStatus(): OfflineOrchestratorStatus {
    return OfflineOrchestratorStatus(
      isRunning = isRunning,
      isInitialized = isEngineInitialized,
      isInitializing = initializationInProgress,
      isPausedByUser = isPausedByUser,
      engineStatus = offlineEngine.getStatus()
    )
  }
}

/**
 * 离线协调器状态
 */
data class OfflineOrchestratorStatus(
  val isRunning: Boolean,
  val isInitialized: Boolean,
  val isInitializing: Boolean,
  val isPausedByUser: Boolean,
  val engineStatus: OfflineEngineStatus
)
