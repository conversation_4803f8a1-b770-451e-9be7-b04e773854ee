package com.aispeech.hybridspeech.asr.offline

import android.util.Log
import com.aispeech.AIError
import com.aispeech.AIResult
import com.aispeech.export.config.AILocalLASRConfig
import com.aispeech.export.config.MagnusRuntimeConfig
import com.aispeech.export.config.MagnusRuntimeEnvInfo
import com.aispeech.export.engines2.AILocalLASREngine
import com.aispeech.export.engines2.MagnusRuntimeHelper
import com.aispeech.export.intent.AILocalLASRIntent
import com.aispeech.export.listeners.AIASRListener
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.lite.MagnusRuntimeTask
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * 离线转写引擎
 * 本地离线转写引擎，处理PCM数据并生成转写结果
 */
class OfflineEngine : CoroutineScope {
  companion object {
    private const val TAG = "OfflineEngine"
    private const val FRAME_SIZE_MS = 20 // 20ms处理一次，降低延迟
    private const val SAMPLE_RATE = 16000
    private const val BYTES_PER_SAMPLE = 2 // 16位PCM
    private const val FRAME_SIZE_BYTES = SAMPLE_RATE * BYTES_PER_SAMPLE * FRAME_SIZE_MS / 1000 // 640字节
    private const val SDK_ASSET_VERSION = 2
    private const val RING_BUFFER_SIZE = 64 * 1024 // 64KB环形缓冲区，可缓存约2秒音频
    private const val CHANNEL_CAPACITY = 50 // Channel容量
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private val _transcriptionResultFlow = MutableSharedFlow<String>(
    replay = 0,
    extraBufferCapacity = 100
  )
  val transcriptionResultFlow: SharedFlow<String> = _transcriptionResultFlow.asSharedFlow()

  private var processingJob: Job? = null
  private val ringBuffer = RingBuffer(RING_BUFFER_SIZE)

  private lateinit var pcmChannel: Channel<ByteArray>

  private var modelPath: String = ""
  private var aiLocalLASREngine: AILocalLASREngine? = null
  private var isMagnusInitialized = false
  private var initializationJob: Job? = null

  // 状态机 - 替换所有布尔状态变量
  @Volatile
  private var currentState: EngineState = EngineState.Idle

  // 状态变化监听器
  private var statusChangeListener: ((OfflineEngineStatus) -> Unit)? = null

  /**
   * 状态转换方法
   */
  private fun transitionTo(newState: EngineState) {
    val oldState = currentState
    currentState = newState
    Log.d(TAG, "State transition: ${oldState::class.simpleName} -> ${newState::class.simpleName}")
    notifyStatusChange()
  }

  /**
   * 获取当前状态的PCM缓存队列（如果处于Initializing状态）
   * 这个方法可以用于调试或监控目的
   */
  fun getPcmBuffer(): ConcurrentLinkedQueue<ByteArray>? {
    return when (val state = currentState) {
      is EngineState.Initializing -> state.pcmBuffer
      else -> null
    }
  }

  /**
   * 设置模型路径
   */
  fun setModelPath(path: String) {
    this.modelPath = path
    Log.i(TAG, "Model path set: $path")
  }

  /**
   * 设置状态变化监听器
   */
  fun setStatusChangeListener(listener: (OfflineEngineStatus) -> Unit) {
    this.statusChangeListener = listener
  }

  /**
   * 通知状态变化
   */
  private fun notifyStatusChange() {
    statusChangeListener?.invoke(getStatus())
  }

  /**
   * 异步初始化引擎
   */
  fun initializeAsync(): Boolean {
    try {
      when (currentState) {
        is EngineState.Initializing -> {
          Log.w(TAG, "Initialization already in progress")
          return false
        }
        is EngineState.Ready -> {
          Log.w(TAG, "Engine already initialized")
          return true
        }
        is EngineState.Running, is EngineState.Paused -> {
          Log.w(TAG, "Engine is already running, cannot reinitialize")
          return false
        }
        is EngineState.Error -> {
          Log.i(TAG, "Recovering from error state, starting initialization")
        }
        is EngineState.Idle -> {
          Log.i(TAG, "Starting initialization from idle state")
        }
      }

      // 转换到初始化状态，创建PCM缓存队列
      val pcmBuffer = ConcurrentLinkedQueue<ByteArray>()
      transitionTo(EngineState.Initializing(pcmBuffer))

      // 启动异步初始化协程
      initializationJob = launch {
        performInitialization()
      }

      Log.i(TAG, "Offline engine async initialization started...")
      return true

    } catch (e: Exception) {
      Log.e(TAG, "Error starting async initialization", e)
      transitionTo(EngineState.Error(AIError(), currentState))
      return false
    }
  }

  /**
   * 执行实际的初始化工作
   */
  private suspend fun performInitialization() {
    try {
      // 使用默认路径如果没有设置
      if (modelPath.isEmpty()) {
        modelPath = getDefaultModelPath()
        Log.i(TAG, "Using default model path: $modelPath")
      }

      // 在IO线程中执行初始化
      withContext(Dispatchers.IO) {
        // 初始化Magnus运行时环境
        if (!initializeMagnusRuntime()) {
          Log.e(TAG, "Failed to initialize Magnus runtime")
          transitionTo(EngineState.Error(AIError(), currentState))
          return@withContext
        }

        // 创建AILocalLASREngine实例
        aiLocalLASREngine = AILocalLASREngine.createInstance()
        if (aiLocalLASREngine == null) {
          Log.e(TAG, "Failed to create AILocalLASREngine instance")
          transitionTo(EngineState.Error(AIError(), currentState))
          return@withContext
        }

        // 配置LASR引擎
        val lasrConfig = AILocalLASRConfig.Builder()
          .setTaskName(MagnusRuntimeTask.MODEL_LASR)
          .build()

        // 初始化引擎 - 这里会触发onInit回调
        aiLocalLASREngine!!.init(lasrConfig, createAsrListener())
      }

      Log.i(TAG, "Offline engine initialization completed, waiting for onInit callback...")

    } catch (e: Exception) {
      Log.e(TAG, "Error during initialization", e)
      transitionTo(EngineState.Error(AIError(), currentState))
    }
  }

  /**
   * 同步初始化引擎（向后兼容）
   * @deprecated 使用 initializeAsync() 代替
   */
  @Deprecated("Use initializeAsync() instead", ReplaceWith("initializeAsync()"))
  fun initialize(): Boolean {
    return initializeAsync()
  }

  /**
   * 获取默认模型路径
   */
  private fun getDefaultModelPath(): String {
    return "/sdcard/hybrid_speech_debug/offline_model"
  }

  /**
   * 开始处理
   * 支持在初始化期间启动，但只有在引擎完全初始化后才会真正开始处理
   */
  fun start(): Boolean {
    try {
      when (val state = currentState) {
        is EngineState.Running -> {
          Log.w(TAG, "Already running")
          return true
        }
        is EngineState.Paused -> {
          Log.i(TAG, "Resuming from paused state")
          transitionTo(EngineState.Running)
          return true
        }
        is EngineState.Initializing -> {
          Log.i(TAG, "Engine is initializing, will start processing once initialization completes")
          // 保持在Initializing状态，但标记需要在初始化完成后启动
          return true
        }
        is EngineState.Ready -> {
          Log.i(TAG, "Starting engine processing from ready state")
          return startEngineProcessing()
        }
        is EngineState.Idle -> {
          Log.e(TAG, "Engine not initialized, cannot start")
          return false
        }
        is EngineState.Error -> {
          Log.e(TAG, "Engine in error state: ${state.error}, cannot start")
          return false
        }
      }

      return startEngineProcessing()

    } catch (e: Exception) {
      Log.e(TAG, "Error starting offline engine", e)
      return false
    }
  }

  /**
   * 启动引擎处理（内部方法）
   */
  private fun startEngineProcessing(): Boolean {
    try {
      // 检查引擎是否已初始化
      if (aiLocalLASREngine == null) {
        Log.e(TAG, "Engine not initialized, cannot start processing")
        return false
      }

      // 为新的处理会话创建一个新的 Channel
      pcmChannel = Channel(
        capacity = CHANNEL_CAPACITY,
        onBufferOverflow = BufferOverflow.DROP_LATEST
      )

      val param = AILocalLASRIntent().apply {
        isUseCustomFeed = true
        isUseTxtSmooth = true
        isUseWpInRec = true
        isUseSensitiveWdsNorm = true
        isUseStreamChar = false
        isUseTprocess = true
        isUsePhrase = true
      }
      aiLocalLASREngine!!.start(param)

      processingJob = launch {
        processingLoop()
      }

      // 转换到运行状态
      transitionTo(EngineState.Running)

      Log.i(TAG, "Offline engine processing started")
      return true

    } catch (e: Exception) {
      Log.e(TAG, "Error starting engine processing", e)
      transitionTo(EngineState.Error(AIError(), currentState))
      return false
    }
  }

  /**
   * 停止处理
   */
  fun stop(timeoutMs: Long = 500) {
    when (val state = currentState) {
      is EngineState.Running, is EngineState.Paused -> {
        Log.i(TAG, "Stopping offline engine...")

        try {
          if (::pcmChannel.isInitialized && !pcmChannel.isClosedForSend) {
            pcmChannel.close()
            Log.d(TAG, "PCM channel closed.")
          }
        } catch (e: Exception) {
          Log.w(TAG, "Exception while closing channel, might be already closed.", e)
        }

        runBlocking {
          try {
            withTimeoutOrNull(timeoutMs) {
              processingJob?.join()
              Log.d(TAG, "Processing job finished joining.")
            }
          } catch (e: Exception) {
            Log.e(TAG, "Error waiting for processing job to join", e)
          }
        }

        flushRemainingAudio()
        Log.d(TAG, "Flushed remaining audio from RingBuffer.")

        aiLocalLASREngine?.stop()
        Log.d(TAG, "Called engine.stop().")

        processingJob = null
        ringBuffer.clear()

        // 转换到Ready状态（保持引擎初始化状态）
        transitionTo(EngineState.Ready(modelPath))

        Log.i(TAG, "Offline engine stopped successfully.")
      }
      is EngineState.Initializing -> {
        Log.i(TAG, "Stopping engine during initialization...")

        // 取消初始化并清空缓存
        initializationJob?.cancel()
        initializationJob = null

        launch {
          clearCachedPcmData(state.pcmBuffer)
        }

        // 转换到Idle状态
        transitionTo(EngineState.Idle)

        Log.i(TAG, "Engine initialization cancelled and stopped.")
      }
      is EngineState.Ready -> {
        Log.w(TAG, "Engine already stopped (in Ready state)")
      }
      is EngineState.Idle, is EngineState.Error -> {
        Log.w(TAG, "Engine not running, stop command ignored (current state: ${state::class.simpleName})")
      }
    }
  }

  /**
   * 关闭引擎协程作用域
   * 取消所有子协程，但不影响其他模块
   */
  fun shutdown(reason: String = "OfflineEngine shutdown") {
    scopeDelegate.shutdown(reason)
    Log.i(TAG, "Offline engine scope shut down: $reason")
  }

  /**
   * 释放资源
   */
  fun release() {
    // 先停止引擎
    stop()

    // 取消初始化任务
    initializationJob?.cancel()
    initializationJob = null

    // 清空缓存的PCM数据（如果有的话）
    when (val state = currentState) {
      is EngineState.Initializing -> {
        runBlocking {
          clearCachedPcmData(state.pcmBuffer)
        }
      }
      else -> {
        // 其他状态下没有缓存数据需要清理
      }
    }

    // 销毁引擎
    aiLocalLASREngine?.destroy()
    aiLocalLASREngine = null

    // 转换到Idle状态
    transitionTo(EngineState.Idle)

    // 关闭协程作用域
    shutdown("Engine released")
    Log.i(TAG, "Offline engine released")
  }

  /**
   * 暂停处理（清空缓存但保留引擎状态）
   */
  fun pause() {
    when (currentState) {
      is EngineState.Running -> {
        Log.i(TAG, "Pausing offline engine processing")

        try {
          // 停止处理循环
          processingJob?.cancel()
          processingJob = null

          // 清空缓存数据，释放内存
          clearBuffers()

          // 转换到暂停状态
          transitionTo(EngineState.Paused)

          Log.i(TAG, "Offline engine paused successfully")
        } catch (e: Exception) {
          Log.e(TAG, "Error pausing offline engine", e)
          transitionTo(EngineState.Error(AIError(), currentState))
        }
      }
      is EngineState.Paused -> {
        Log.w(TAG, "Already paused")
      }
      else -> {
        Log.w(TAG, "Cannot pause: not running (current state: ${currentState::class.simpleName})")
      }
    }
  }

  /**
   * 恢复处理
   */
  fun resume() {
    when (currentState) {
      is EngineState.Paused -> {
        Log.i(TAG, "Resuming offline engine processing")

        try {
          // 重新创建Channel，因为暂停时可能已经关闭
          pcmChannel = Channel(
            capacity = CHANNEL_CAPACITY,
            onBufferOverflow = BufferOverflow.DROP_LATEST
          )

          // 重新启动处理循环
          processingJob = launch {
            processingLoop()
          }

          // 转换到运行状态
          transitionTo(EngineState.Running)

          Log.i(TAG, "Offline engine resumed successfully")
        } catch (e: Exception) {
          Log.e(TAG, "Error resuming offline engine", e)
          transitionTo(EngineState.Error(AIError(), currentState))
        }
      }
      is EngineState.Running -> {
        Log.w(TAG, "Not paused, no need to resume")
      }
      else -> {
        Log.w(TAG, "Cannot resume: not in paused state (current state: ${currentState::class.simpleName})")
      }
    }
  }

  /**
   * 清空所有缓存数据
   */
  private fun clearBuffers() {
    try {
      // 清空环形缓冲区
      ringBuffer.clear()

      Log.d(TAG, "Cleared all buffers")
    } catch (e: Exception) {
      Log.e(TAG, "Error clearing buffers", e)
    }
  }

  /**
   * 处理PCM数据
   * 根据当前状态决定是缓存数据还是直接发送
   */
  fun processPcmData(pcmData: ByteArray) {
    when (val state = currentState) {
      is EngineState.Initializing -> {
        // 引擎正在初始化，缓存PCM数据
        state.pcmBuffer.offer(pcmData.copyOf())
        Log.v(TAG, "Cached PCM data during initialization, buffer size: ${state.pcmBuffer.size}")
      }
      is EngineState.Running -> {
        // 引擎正在运行，直接发送数据
        if (::pcmChannel.isInitialized) {
          pcmChannel.trySend(pcmData)
        }
      }
      is EngineState.Ready, is EngineState.Paused, is EngineState.Idle, is EngineState.Error -> {
        // 其他状态下忽略数据
        Log.v(TAG, "Ignoring PCM data in state: ${state::class.simpleName}")
      }
    }
  }

  /**
   * 处理循环
   */
  private suspend fun processingLoop() {
    val frame = ByteArray(FRAME_SIZE_BYTES)          // 20ms → 640字节
    try {
      for (data in pcmChannel) {                     // 挂起式消费
        ringBuffer.write(data)
        while (ringBuffer.read(frame)) {
          aiLocalLASREngine?.feedData(frame, frame.size)
        }
      }
    } catch (e: Exception) {
      Log.e(TAG, "processingLoop error", e)
    } finally {
      // 如果当前状态不是暂停状态，则转换到就绪状态
      // 这表示处理循环正常结束或因错误结束，但不是因为暂停
      if (currentState !is EngineState.Paused) {
        when (currentState) {
          is EngineState.Running -> {
            // 从运行状态正常结束，转换到就绪状态
            transitionTo(EngineState.Ready(modelPath))
          }
          is EngineState.Error -> {
            // 已经是错误状态，不需要改变
          }
          else -> {
            // 其他状态下结束处理循环，转换到就绪状态
            transitionTo(EngineState.Ready(modelPath))
          }
        }
      }
    }
  }

  /**
   * 处理初始化期间缓存的PCM数据
   */
  private suspend fun processCachedPcmData(pcmBuffer: ConcurrentLinkedQueue<ByteArray>) {
    try {
      if (pcmBuffer.isNotEmpty()) {
        val cacheSize = pcmBuffer.size
        Log.i(TAG, "Processing $cacheSize cached PCM data chunks")

        // 如果引擎正在运行，直接发送缓存的数据
        when (currentState) {
          is EngineState.Running -> {
            if (::pcmChannel.isInitialized) {
              var cachedData = pcmBuffer.poll()
              while (cachedData != null) {
                pcmChannel.trySend(cachedData)
                cachedData = pcmBuffer.poll()
              }
            }
          }
          else -> {
            // 如果引擎没有运行，清空缓存
            pcmBuffer.clear()
          }
        }

        Log.i(TAG, "Cached PCM data processed and cleared")
      }
    } catch (e: Exception) {
      Log.e(TAG, "Error processing cached PCM data", e)
    }
  }

  /**
   * 清空缓存的PCM数据
   */
  private suspend fun clearCachedPcmData(pcmBuffer: ConcurrentLinkedQueue<ByteArray>) {
    try {
      val cacheSize = pcmBuffer.size
      pcmBuffer.clear()
      Log.i(TAG, "Cleared $cacheSize cached PCM data chunks due to initialization failure")
    } catch (e: Exception) {
      Log.e(TAG, "Error clearing cached PCM data", e)
    }
  }

  /**
   * 刷新剩余音频数据
   */
  private fun flushRemainingAudio() {
    try {
      val remainingSize = ringBuffer.size()
      if (remainingSize > 0) {
        val lastBytes = ByteArray(remainingSize)
        if (ringBuffer.read(lastBytes)) {
          aiLocalLASREngine?.feedData(lastBytes, lastBytes.size)
        }
      }
    } catch (e: Exception) {
      Log.e(TAG, "Error flushing remaining audio", e)
    }
  }

  /**
   * 获取引擎状态
   */
  fun getStatus(): OfflineEngineStatus {
    val state = currentState
    return OfflineEngineStatus(
      isRunning = state is EngineState.Running,
      isInitialized = state is EngineState.Ready || state is EngineState.Running || state is EngineState.Paused,
      isInitializing = state is EngineState.Initializing,
      isPaused = state is EngineState.Paused,
      modelPath = when (state) {
        is EngineState.Ready -> state.modelPath
        is EngineState.Running, is EngineState.Paused -> modelPath
        else -> modelPath
      },
      bufferSize = ringBuffer.size(),
      queueSize = if (::pcmChannel.isInitialized) {
        pcmChannel.tryReceive().getOrNull()?.let {
          pcmChannel.trySend(it)
          1
        } ?: 0
      } else {
        0
      },
      cachedDataCount = when (state) {
        is EngineState.Initializing -> state.pcmBuffer.size
        else -> 0
      },
      engineState = state
    )
  }

  /**
   * 初始化Magnus运行时环境
   */
  private fun initializeMagnusRuntime(): Boolean {
    try {
      if (isMagnusInitialized) {
        Log.i(TAG, "Magnus runtime already initialized")
        return true
      }

      val magnusRuntimeEnvInfo = MagnusRuntimeEnvInfo.Builder()
        .setType(MagnusRuntimeEnvInfo.TYPE_CLIENT)
        .setLogLevel(Log.WARN)
        .create()
//
//      val assetFolderPath = "magnus_with_translate"
//      val targetSubdirectoryName = "magnusRes"
//
//      val copiedConfigPath = AssetCopyUtil.copyAssetsToInternalStorage(
//        context,
//        assetFolderPath,
//        targetSubdirectoryName,
//        SDK_ASSET_VERSION
//      )

      MagnusRuntimeHelper.getInstance().init(
        MagnusRuntimeConfig.Builder()
          .setPath(modelPath)
          .setInfo(magnusRuntimeEnvInfo.toJson())
          .setServerType(true)
          .create()
      )

      isMagnusInitialized = true
      Log.i(TAG, "Magnus runtime initialized successfully")
      return true

    } catch (e: Exception) {
      Log.e(TAG, "Error initializing Magnus runtime", e)
      return false
    }
  }

  /**
   * 创建ASR监听器
   */
  private fun createAsrListener(): AIASRListener {
    return object : AIASRListener {
      override fun onInit(p0: Int) {
        Log.d(TAG, "onInit status: $p0")

        when (val state = currentState) {
          is EngineState.Initializing -> {
            if (p0 == 0) {
              Log.i(TAG, "Engine initialization completed successfully")

              // 转换到Ready状态
              transitionTo(EngineState.Ready(modelPath))

              // 启动引擎处理并处理缓存数据
              if (startEngineProcessing()) {
                launch {
                  processCachedPcmData(state.pcmBuffer)
                }
              } else {
                // 启动失败，清空缓存
                launch {
                  clearCachedPcmData(state.pcmBuffer)
                }
              }
            } else {
              Log.e(TAG, "Engine initialization failed with status: $p0")

              // 初始化失败，转换到错误状态并清空缓存
              val error = AIError() // 可以根据p0设置具体错误信息
              transitionTo(EngineState.Error(error, state))

              launch {
                clearCachedPcmData(state.pcmBuffer)
              }
            }
          }
          else -> {
            Log.w(TAG, "Received onInit callback in unexpected state: ${state::class.simpleName}")
          }
        }

        // 通知状态变化
        notifyStatusChange()
      }

      override fun onError(p0: AIError?) {
        Log.d(TAG, "onError error: $p0")
      }

      override fun onReadyForSpeech() {
        Log.d(TAG, "onReadyForSpeech")
      }

      override fun onResultDataReceived(p0: ByteArray?, p1: Int) {
        // 不需要处理
      }

      override fun onResultDataReceived(p0: ByteArray?, p1: Int, p2: Int) {
        // 不需要处理
      }

      override fun onRawDataReceived(p0: ByteArray?, p1: Int) {
        // 不需要处理
      }

      override fun onResults(result: AIResult?) {
        Log.d(TAG, "onResults: $result")
        launch {
          _transcriptionResultFlow.emit(result?.resultJSONObject.toString())
        }
      }

      override fun onRmsChanged(p0: Float) {
        // 音量变化，可以根据需要处理
      }

      override fun onBeginningOfSpeech() {
        Log.d(TAG, "onBeginningOfSpeech")
      }

      override fun onEndOfSpeech() {
        Log.d(TAG, "onEndOfSpeech")
      }

      override fun onNotOneShot() {
        Log.d(TAG, "onNotOneShot")
      }

    }
  }

}

/**
 * 使用固定长度的环形缓冲区，线程安全且零拷贝
 */
private class RingBuffer(capacity: Int) {
  private val buf = ByteArray(capacity)
  private var readPos = 0
  private var writePos = 0
  private var available = 0
  private val lock = Any()

  private fun discard(count: Int) {
    val real = minOf(count, available)
    readPos = (readPos + real) % buf.size
    available -= real
  }

  fun write(src: ByteArray) = synchronized(lock) {
    if (src.size > buf.size - available) {
      discard(src.size - (buf.size - available))
    }
    var offset = 0
    var len = src.size
    while (len > 0) {
      val space = minOf(len, buf.size - writePos)
      System.arraycopy(src, offset, buf, writePos, space)
      writePos = (writePos + space) % buf.size
      offset += space
      len -= space
      available += space
    }
  }

  fun read(dst: ByteArray): Boolean {
    synchronized(lock) {
      if (available < dst.size) return false
      var offset = 0
      var len = dst.size
      while (len > 0) {
        val chunk = minOf(len, buf.size - readPos)
        System.arraycopy(buf, readPos, dst, offset, chunk)
        readPos = (readPos + chunk) % buf.size
        offset += chunk
        len -= chunk
        available -= chunk
      }
      return true
    }
  }

  fun size(): Int = synchronized(lock) { available }

  // 添加清空缓冲区的方法
  fun clear() = synchronized(lock) {
    readPos = 0
    writePos = 0
    available = 0
  }
}

/**
 * OfflineEngine 的状态机密封类 (增强版)
 *
 * 这个版本专门为"初始化时缓存数据"的场景设计，能够清晰地表达引擎的生命周期。
 */
sealed class EngineState {

    /**
     * 空闲状态。
     * 引擎的初始状态，或在 release() 后进入此状态。
     * - 可转换至: [Initializing]
     */
    object Idle : EngineState()

    /**
     * 正在初始化状态。
     * 已经调用 start()，引擎正在进行后台初始化。
     * 在这个状态下，引擎可以接收并缓存音频数据。
     * @param pcmBuffer 用于缓存音频数据的线程安全队列。
     * - 可转换至: [Ready], [Error]
     */
    data class Initializing(val pcmBuffer: ConcurrentLinkedQueue<ByteArray>) : EngineState()

    /**
     * 已就绪状态。
     * 引擎已成功初始化，但尚未处理音频流（例如，刚初始化完成，或 stop() 之后）。
     * @param modelPath 当前使用的模型路径。
     * - 可转换至: [Running], [Idle] (通过 release)
     */
    data class Ready(val modelPath: String) : EngineState()

    /**
     * 正在运行状态。
     * 引擎已初始化并正在积极处理音频数据。
     * - 可转换至: [Paused], [Ready] (通过 stop), [Error]
     */
    object Running : EngineState()

    /**
     * 暂停状态。
     * 引擎正在运行但被暂停，不处理新的音频数据。
     * - 可转换至: [Running] (通过 resume), [Ready] (通过 stop)
     */
    object Paused : EngineState()

    /**
     * 错误状态。
     * 引擎在任何阶段发生不可恢复的错误。
     * @param error 具体的错误信息。
     * @param previousState 发生错误前的状态，便于调试和恢复。
     * - 可转换至: [Idle] (通过 release)
     */
    data class Error(val error: AIError, val previousState: EngineState) : EngineState()
}

/**
 * 离线引擎状态（向后兼容）
 */
data class OfflineEngineStatus(
  val isRunning: Boolean,
  val isInitialized: Boolean,
  val isInitializing: Boolean,
  val isPaused: Boolean,
  val modelPath: String,
  val bufferSize: Int,
  val queueSize: Int,
  val cachedDataCount: Int = 0, // 初始化期间缓存的PCM数据包数量
  val engineState: EngineState // 新增状态机状态
)
